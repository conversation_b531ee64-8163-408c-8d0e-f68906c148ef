<?php
/**
 * Category Creation Test Script
 * 
 * This script tests the category creation functionality to help diagnose issues.
 * Run this script to verify that category creation is working properly.
 */

require_once __DIR__ . '/../bootstrap.php';
require_once __DIR__ . '/../components/admin/index.php';
require_once __DIR__ . '/../components/admin/categories/index.php';

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Creation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>Category Creation Test Results</h1>
    <p>This page tests the category creation functionality to help diagnose any issues.</p>

    <?php
    $testResults = [];
    $overallSuccess = true;

    // Test 1: Database Connection
    echo '<div class="test-section">';
    echo '<h2>Test 1: Database Connection</h2>';
    try {
        $connection = getMongoConnection();
        $isMongoAvailable = is_mongodb_available();
        $isDatabaseAvailable = is_database_available();
        $connectionType = $connection->isUsingMock() ? 'Mock Database' : 'MongoDB';
        
        echo '<div class="test-result success">';
        echo '<strong>✓ Database Connection Successful</strong><br>';
        echo 'Connection Type: ' . htmlspecialchars($connectionType) . '<br>';
        echo 'MongoDB Available: ' . ($isMongoAvailable ? 'Yes' : 'No') . '<br>';
        echo 'Database Available: ' . ($isDatabaseAvailable ? 'Yes' : 'No');
        echo '</div>';
        
        $testResults['database'] = true;
    } catch (Exception $e) {
        echo '<div class="test-result error">';
        echo '<strong>✗ Database Connection Failed</strong><br>';
        echo 'Error: ' . htmlspecialchars($e->getMessage());
        echo '</div>';
        
        $testResults['database'] = false;
        $overallSuccess = false;
    }
    echo '</div>';

    // Test 2: Repository Creation
    echo '<div class="test-section">';
    echo '<h2>Test 2: Category Repository</h2>';
    try {
        $repository = new CategoryRepository();
        $categories = $repository->find();
        
        echo '<div class="test-result success">';
        echo '<strong>✓ Category Repository Working</strong><br>';
        echo 'Total Categories: ' . count($categories);
        echo '</div>';
        
        $testResults['repository'] = true;
    } catch (Exception $e) {
        echo '<div class="test-result error">';
        echo '<strong>✗ Category Repository Failed</strong><br>';
        echo 'Error: ' . htmlspecialchars($e->getMessage());
        echo '</div>';
        
        $testResults['repository'] = false;
        $overallSuccess = false;
    }
    echo '</div>';

    // Test 3: Service Layer
    echo '<div class="test-section">';
    echo '<h2>Test 3: Category Service</h2>';
    try {
        $repository = new CategoryRepository();
        $service = new CategoryService($repository);
        $statusOptions = $service->getStatusOptions();
        
        echo '<div class="test-result success">';
        echo '<strong>✓ Category Service Working</strong><br>';
        echo 'Available Status Options: ' . implode(', ', array_keys($statusOptions));
        echo '</div>';
        
        $testResults['service'] = true;
    } catch (Exception $e) {
        echo '<div class="test-result error">';
        echo '<strong>✗ Category Service Failed</strong><br>';
        echo 'Error: ' . htmlspecialchars($e->getMessage());
        echo '</div>';
        
        $testResults['service'] = false;
        $overallSuccess = false;
    }
    echo '</div>';

    // Test 4: Category Creation
    echo '<div class="test-section">';
    echo '<h2>Test 4: Category Creation</h2>';
    if ($testResults['database'] && $testResults['repository'] && $testResults['service']) {
        try {
            $repository = new CategoryRepository();
            $service = new CategoryService($repository);
            
            $testData = [
                'name' => 'Test Category ' . time(),
                'description' => 'This is a test category created by the diagnostic script',
                'status' => 'active'
            ];
            
            $result = $service->createCategory($testData);
            
            if ($result['success']) {
                echo '<div class="test-result success">';
                echo '<strong>✓ Category Creation Successful</strong><br>';
                echo 'Test category created successfully!<br>';
                echo 'Category Name: ' . htmlspecialchars($testData['name']);
                echo '</div>';
                
                $testResults['creation'] = true;
            } else {
                echo '<div class="test-result error">';
                echo '<strong>✗ Category Creation Failed</strong><br>';
                echo 'Errors: ' . htmlspecialchars(json_encode($result['errors']));
                echo '</div>';
                
                $testResults['creation'] = false;
                $overallSuccess = false;
            }
        } catch (Exception $e) {
            echo '<div class="test-result error">';
            echo '<strong>✗ Category Creation Exception</strong><br>';
            echo 'Error: ' . htmlspecialchars($e->getMessage());
            echo '</div>';
            
            $testResults['creation'] = false;
            $overallSuccess = false;
        }
    } else {
        echo '<div class="test-result warning">';
        echo '<strong>⚠ Category Creation Skipped</strong><br>';
        echo 'Previous tests failed, skipping category creation test.';
        echo '</div>';
        
        $testResults['creation'] = false;
        $overallSuccess = false;
    }
    echo '</div>';

    // Test 5: Environment Information
    echo '<div class="test-section">';
    echo '<h2>Test 5: Environment Information</h2>';
    echo '<div class="test-result info">';
    echo '<strong>Environment Details:</strong><br>';
    echo 'PHP Version: ' . PHP_VERSION . '<br>';
    echo 'Session Status: ' . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . '<br>';
    echo 'MongoDB Extension: ' . (extension_loaded('mongodb') ? 'Loaded' : 'Not Loaded') . '<br>';
    echo 'Server Software: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '<br>';
    echo 'Request Method: ' . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . '<br>';
    echo '</div>';
    echo '</div>';

    // Overall Result
    echo '<div class="test-section">';
    echo '<h2>Overall Test Result</h2>';
    if ($overallSuccess) {
        echo '<div class="test-result success">';
        echo '<strong>✓ All Tests Passed!</strong><br>';
        echo 'Category creation functionality is working correctly. If you\'re still experiencing issues, ';
        echo 'please check your browser\'s developer console for JavaScript errors or try accessing ';
        echo '<a href="add-category.php?debug=1">add-category.php?debug=1</a> for additional debugging information.';
        echo '</div>';
    } else {
        echo '<div class="test-result error">';
        echo '<strong>✗ Some Tests Failed</strong><br>';
        echo 'There are issues with the category creation functionality. Please review the failed tests above ';
        echo 'and contact your system administrator for assistance.';
        echo '</div>';
    }
    echo '</div>';
    ?>

    <div class="test-section">
        <h2>Next Steps</h2>
        <ul>
            <li><a href="add-category.php">Go to Add Category Page</a></li>
            <li><a href="add-category.php?debug=1">Go to Add Category Page (Debug Mode)</a></li>
            <li><a href="categories.php">View All Categories</a></li>
            <li><a href="test-category-creation.php">Run This Test Again</a></li>
        </ul>
    </div>
</body>
</html>
