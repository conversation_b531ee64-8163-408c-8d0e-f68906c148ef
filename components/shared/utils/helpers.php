<?php

if (!function_exists('site_config')) {
    function site_config(?string $path = null, $default = null)
    {
        static $config;
        if ($config === null) {
            $config = require __DIR__ . '/../../../config/site.php';
        }

        if ($path === null) {
            return $config;
        }

        $segments = explode('.', $path);
        $value = $config;

        foreach ($segments as $segment) {
            if (is_array($value) && array_key_exists($segment, $value)) {
                $value = $value[$segment];
            } else {
                return $default;
            }
        }

        return $value;
    }
}

if (!function_exists('array_access')) {
    function array_access(array $array, string $key, $default = null)
    {
        return $array[$key] ?? $default;
    }
}

if (!function_exists('html_attr')) {
    function html_attr(string $name, ?string $value): string
    {
        if ($value === null || $value === '') {
            return '';
        }

        return sprintf(' %s="%s"', $name, htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
    }
}

if (!function_exists('html_classes')) {
    function html_classes(array $classes): string
    {
        $filtered = array_filter(array_map('trim', $classes));
        if (empty($filtered)) {
            return '';
        }

        return htmlspecialchars(implode(' ', $filtered), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('render_partial')) {
    function render_partial(string $path, array $variables = []): void
    {
        extract($variables, EXTR_OVERWRITE);
        include $path;
    }
}

if (!function_exists('render_nav_links')) {
    /**
     * Renders a simple unordered list of navigation links.
     */
    function render_nav_links(array $links, array $options = []): void
    {
        $linkClass = $options['link_class'] ?? '';
        foreach ($links as $link) {
            $href = $link['href'] ?? '#';
            $label = $link['label'] ?? '';
            $class = trim($linkClass . ' ' . ($link['class'] ?? ''));
            echo '<li><a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '"' . ($class ? html_attr('class', $class) : '') . '>' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</a></li>';
        }
    }
}

if (!function_exists('render_category_menu')) {
    /**
     * Renders the multi-level category menu structure.
     */
    function render_category_menu(array $categories, array $options = []): void
    {
        $itemLinkClass = $options['item_link_class'] ?? 'menu-item';
        $submenuClass = $options['submenu_class'] ?? 'submenu';
        $submenuLinkClass = $options['submenu_link_class'] ?? 'mobile-menu-link';
        $showIcons = $options['show_icons'] ?? true;

        foreach ($categories as $category) {
            $label = $category['label'] ?? '';
            $href = $category['href'] ?? '#';
            $icon = $category['icon'] ?? null;
            $submenu = $category['submenu'] ?? [];
            $hasSubmenu = !empty($submenu);

            echo '<li>';
            echo '<a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '" class="' . htmlspecialchars($itemLinkClass, ENT_QUOTES, 'UTF-8') . '">';
            if ($showIcons && $icon) {
                echo '<img src="' . htmlspecialchars($icon, ENT_QUOTES, 'UTF-8') . '" alt="icons">';
            }
            echo '<span>' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</span>';
            if ($hasSubmenu) {
                echo '<i class="fa-regular fa-plus"></i>';
            }
            echo '</a>';

            if ($hasSubmenu) {
                echo '<ul class="' . htmlspecialchars($submenuClass, ENT_QUOTES, 'UTF-8') . '">';
                foreach ($submenu as $submenuLabel) {
                    if (is_array($submenuLabel)) {
                        // support associative submenu definitions
                        $submenuHref = $submenuLabel['href'] ?? '#';
                        $submenuText = $submenuLabel['label'] ?? '';
                    } else {
                        $submenuHref = '#';
                        $submenuText = $submenuLabel;
                    }
                    echo '<li><a class="' . htmlspecialchars($submenuLinkClass, ENT_QUOTES, 'UTF-8') . '" href="' . htmlspecialchars($submenuHref, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($submenuText, ENT_QUOTES, 'UTF-8') . '</a></li>';
                }
                echo '</ul>';
            }

            echo '</li>';
        }
    }
}

if (!function_exists('resolve_category_image_url')) {
    function resolve_category_image_url(?string $path): ?string
    {
        if ($path === null) {
            return null;
        }

        $trimmed = trim($path);
        if ($trimmed === '') {
            return null;
        }

        if (preg_match('/^https?:\/\//i', $trimmed) === 1) {
            return $trimmed;
        }

        $parsed = parse_url($trimmed);
        if ($parsed !== false && isset($parsed['path']) && str_ends_with($parsed['path'], 'serve-category-image.php')) {
            return $trimmed;
        }

        $projectRoot = dirname(__DIR__, 3);
        $relative = ltrim($trimmed, '/');
        $absolute = $projectRoot . '/' . $relative;

        if (is_file($absolute)) {
            return $trimmed;
        }

        $filename = basename($relative);
        if (
            $filename !== ''
            && preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|webp)$/i', $filename) === 1
        ) {
            $fallbacks = [
                'assets' => $projectRoot . '/assets/uploads/categories/' . $filename,
                'storage' => $projectRoot . '/storage/uploads/categories/' . $filename,
                'temp' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-category-images/' . $filename,
            ];

            foreach ($fallbacks as $disk => $candidate) {
                if (is_file($candidate)) {
                    return 'serve-category-image.php?disk=' . rawurlencode($disk) . '&file=' . rawurlencode($filename);
                }
            }
        }

        return $trimmed;
    }
}
