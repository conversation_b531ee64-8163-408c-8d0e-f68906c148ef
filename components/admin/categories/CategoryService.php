<?php

class CategoryService
{
    private string $uploadDirectory;
    private string $uploadRelativePath;
    private string $projectRoot;

    /**
     * @var array<int, array{disk: string, absolute: string, public: ?string}>
     */
    private array $uploadLocations;

    public function __construct(private CategoryRepository $repository, ?string $uploadDirectory = null)
    {
        $this->projectRoot = dirname(__DIR__, 3);
        $this->uploadRelativePath = 'assets/uploads/categories';
        $this->uploadDirectory = $uploadDirectory ?? $this->projectRoot . '/' . $this->uploadRelativePath;
        $this->uploadLocations = $this->initialiseUploadLocations($uploadDirectory);
    }

    public function getStatusOptions(): array
    {
        return $this->repository->allStatuses();
    }

    /**
     * @return array{success: bool, errors: array<string, string>, message: string|null}
     */
    public function createCategory(array $input, ?array $imageUpload = null): array
    {
        $statusOptions = $this->getStatusOptions();
        $validation = CategoryValidator::validate($input, $statusOptions, $imageUpload);

        if (!empty($validation['errors'])) {
            return [
                'success' => false,
                'errors' => $validation['errors'],
                'message' => 'Please correct the highlighted errors and try again.',
            ];
        }

        $data = $validation['valid'];
        $imageFile = $data['image_upload'] ?? null;
        unset($data['image_upload']);
        $data['slug'] = $this->generateUniqueSlug($data['name']);

        if ($imageFile !== null) {
            $storageResult = $this->storeCategoryImage($imageFile);
            if (!$storageResult['success']) {
                return [
                    'success' => false,
                    'errors' => ['image' => $storageResult['error']],
                    'message' => 'Please correct the highlighted errors and try again.',
                ];
            }
            $data['image_path'] = $storageResult['path'];
        }

        try {
            $this->repository->create($data);
        } catch (\Throwable $exception) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to save category. Please try again.'],
                'message' => 'We couldn\'t save the category right now.',
            ];
        }

        return [
            'success' => true,
            'errors' => [],
            'message' => 'Category created successfully.',
        ];
    }

    /**
     * @return array{success: bool, errors: array<string, string>, message: string|null}
     */
    public function updateCategory(string $id, array $input, ?array $imageUpload = null): array
    {
        $existing = $this->repository->find($id);
        if ($existing === null) {
            return [
                'success' => false,
                'errors' => ['general' => 'Category not found.'],
                'message' => 'The category you are trying to update no longer exists.',
            ];
        }

        $statusOptions = $this->getStatusOptions();
        $validation = CategoryValidator::validate($input, $statusOptions, $imageUpload);
        if (!empty($validation['errors'])) {
            return [
                'success' => false,
                'errors' => $validation['errors'],
                'message' => 'Please correct the highlighted errors and try again.',
            ];
        }

        $data = $validation['valid'];
        $imageFile = $data['image_upload'] ?? null;
        unset($data['image_upload']);
        $data['slug'] = $this->generateUniqueSlug($data['name'], $existing['id']);

        if ($imageFile !== null) {
            $storageResult = $this->storeCategoryImage($imageFile);
            if (!$storageResult['success']) {
                return [
                    'success' => false,
                    'errors' => ['image' => $storageResult['error']],
                    'message' => 'Please correct the highlighted errors and try again.',
                ];
            }

            if (!empty($existing['image_path'])) {
                $this->deleteCategoryImage($existing['image_path']);
            }

            $data['image_path'] = $storageResult['path'];
        } else {
            $data['image_path'] = $existing['image_path'] ?? null;
        }

        try {
            $this->repository->update($existing['id'], $data);
        } catch (\Throwable $exception) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to update category. Please try again.'],
                'message' => 'We couldn\'t update the category right now.',
            ];
        }

        return [
            'success' => true,
            'errors' => [],
            'message' => 'Category updated successfully.',
        ];
    }

    public function getCategory(string $id): ?array
    {
        return $this->repository->find($id);
    }

    /**
     * @return array{success: bool, errors: array<string, string>, message: string|null}
     */
    public function deleteCategory(string $id): array
    {
        $existing = $this->repository->find($id);
        if ($existing === null) {
            return [
                'success' => false,
                'errors' => ['general' => 'Category not found.'],
                'message' => 'The category you are trying to delete no longer exists.',
            ];
        }

        try {
            $deleted = $this->repository->delete($id);
        } catch (\Throwable $exception) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to delete category. Please try again.'],
                'message' => 'We couldn\'t delete the category right now.',
            ];
        }

        if (!$deleted) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to delete category. Please try again.'],
                'message' => 'We couldn\'t delete the category right now.',
            ];
        }

        if (!empty($existing['image_path'] ?? null)) {
            $this->deleteCategoryImage($existing['image_path']);
        }

        return [
            'success' => true,
            'errors' => [],
            'message' => 'Category deleted successfully.',
        ];
    }

    public function listAllCategories(): array
    {
        return $this->repository->all();
    }

    /**
     * @return array{data: array<int, array<string, mixed>>, total: int, total_pages: int, current_page: int}
     */
    public function paginateCategories(int $page, int $perPage, ?string $search = null, ?string $status = null): array
    {
        $perPage = max(1, $perPage);
        $total = $this->repository->count($search, $status);
        $totalPages = max(1, (int) ceil($total / $perPage));
        $page = max(1, min($page, $totalPages));
        $data = $this->repository->paginate($page, $perPage, $search, $status);

        return [
            'data' => $data,
            'total' => $total,
            'total_pages' => $totalPages,
            'current_page' => $page,
        ];
    }

    private function generateUniqueSlug(string $name, ?string $ignoreId = null): string
    {
        $baseSlug = $this->slugify($name);
        $slug = $baseSlug;
        $counter = 1;

        while (($existing = $this->repository->findBySlug($slug)) !== null) {
            if ($ignoreId !== null && $existing['id'] === $ignoreId) {
                break;
            }
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    private function slugify(string $value): string
    {
        $value = iconv('UTF-8', 'ASCII//TRANSLIT', $value);
        $value = preg_replace('/[^A-Za-z0-9-]+/', '-', $value ?? '');
        $value = strtolower(trim($value, '-'));

        return $value !== '' ? $value : 'category';
    }

    /**
     * @param array{tmp_name:string, extension:string} $imageFile
     * @return array{success: bool, path?: string, error?: string}
     */
    private function storeCategoryImage(array $imageFile): array
    {
        $filename = $this->generateStoredFilename($imageFile);
        if ($filename === null) {
            return [
                'success' => false,
                'error' => 'Unable to generate a file name for the image upload.',
            ];
        }

        $tmpName = $imageFile['tmp_name'];

        foreach ($this->uploadLocations as $location) {
            $absoluteDirectory = $location['absolute'];

            if (!$this->ensureWritableDirectory($absoluteDirectory)) {
                continue;
            }

            $destination = rtrim($absoluteDirectory, "/\\") . '/' . $filename;

            if (!$this->moveUploadedFile($tmpName, $destination)) {
                continue;
            }

            $publicPath = $this->buildPublicPath($location, $filename);
            if ($publicPath === null) {
                @unlink($destination);
                continue;
            }

            return ['success' => true, 'path' => $publicPath];
        }

        return [
            'success' => false,
            'error' => 'Unable to create directory for category images. Please check directory permissions.',
        ];
    }

    private function deleteCategoryImage(?string $relativePath): void
    {
        if ($relativePath === null || $relativePath === '') {
            return;
        }

        $absolutePath = $this->resolveStoredImageAbsolutePath($relativePath);
        if ($absolutePath !== null && is_file($absolutePath)) {
            @unlink($absolutePath);
        }
    }

    /**
     * @return array<int, array{disk: string, absolute: string, public: ?string}>
     */
    private function initialiseUploadLocations(?string $customDirectory): array
    {
        $locations = [];

        if ($customDirectory !== null) {
            $absolute = $this->resolvePathRelativeToRoot($customDirectory);
            $locations[] = [
                'disk' => 'custom',
                'absolute' => $absolute,
                'public' => $this->derivePublicPath($absolute),
            ];
            return $locations;
        }

        $locations[] = [
            'disk' => 'assets',
            'absolute' => $this->projectRoot . '/' . $this->uploadRelativePath,
            'public' => $this->uploadRelativePath,
        ];

        $locations[] = [
            'disk' => 'storage',
            'absolute' => $this->projectRoot . '/storage/uploads/categories',
            'public' => 'storage/uploads/categories',
        ];

        $locations[] = [
            'disk' => 'temp',
            'absolute' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-category-images',
            'public' => null,
        ];

        return $locations;
    }

    private function ensureWritableDirectory(string $directory): bool
    {
        if (is_dir($directory)) {
            return is_writable($directory);
        }

        $parent = dirname($directory);
        if ($parent !== '' && $parent !== $directory && !is_dir($parent)) {
            if (!$this->ensureWritableDirectory($parent)) {
                return false;
            }
        }

        if (!@mkdir($directory, 0775) && !is_dir($directory)) {
            return false;
        }

        @chmod($directory, 0775);

        return is_writable($directory);
    }

    private function moveUploadedFile(string $tmpName, string $destination): bool
    {
        if (@move_uploaded_file($tmpName, $destination)) {
            return true;
        }

        // Fallback for environments where move_uploaded_file is restricted
        return @rename($tmpName, $destination);
    }

    /**
     * @param array{disk: string, absolute: string, public: ?string} $location
     */
    private function buildPublicPath(array $location, string $filename): ?string
    {
        if (!$this->isValidStoredFilename($filename)) {
            return null;
        }

        if ($location['public'] !== null) {
            return rtrim($location['public'], "/\\") . '/' . $filename;
        }

        return 'serve-category-image.php?disk=' . rawurlencode($location['disk']) . '&file=' . rawurlencode($filename);
    }

    private function resolveStoredImageAbsolutePath(string $storedPath): ?string
    {
        $trimmed = trim($storedPath);

        if ($trimmed === '') {
            return null;
        }

        $parsed = parse_url($trimmed);
        if ($parsed !== false && isset($parsed['path']) && str_ends_with($parsed['path'], 'serve-category-image.php')) {
            parse_str($parsed['query'] ?? '', $parameters);
            $disk = $parameters['disk'] ?? null;
            $file = $parameters['file'] ?? null;

            if (is_string($disk) && is_string($file) && $this->isValidStoredFilename($file)) {
                $location = $this->findUploadLocation($disk);
                if ($location !== null) {
                    return rtrim($location['absolute'], "/\\") . '/' . $file;
                }
            }

            return null;
        }

        $absoluteFromRoot = $this->projectRoot . '/' . ltrim($trimmed, '/');
        if (is_file($absoluteFromRoot)) {
            return $absoluteFromRoot;
        }

        return null;
    }

    private function resolvePathRelativeToRoot(string $path): string
    {
        if ($path === '') {
            return $this->projectRoot;
        }

        if ($path[0] === '/' || $path[0] === DIRECTORY_SEPARATOR) {
            return $path;
        }

        if (preg_match('/^[A-Za-z]:[\\\/]/', $path) === 1 || str_starts_with($path, '\\')) {
            return $path;
        }

        return $this->projectRoot . '/' . ltrim($path, '/');
    }

    private function derivePublicPath(string $absolutePath): ?string
    {
        $root = rtrim($this->normalisePath($this->projectRoot), '/') . '/';
        $absoluteNormalised = rtrim($this->normalisePath($absolutePath), '/');

        if (str_starts_with($absoluteNormalised, $root)) {
            return ltrim(substr($absoluteNormalised, strlen($root)), '/');
        }

        return null;
    }

    private function normalisePath(string $path): string
    {
        return str_replace('\\', '/', $path);
    }

    private function findUploadLocation(string $disk): ?array
    {
        foreach ($this->uploadLocations as $location) {
            if ($location['disk'] === $disk) {
                return $location;
            }
        }

        return null;
    }

    private function isValidStoredFilename(string $filename): bool
    {
        return (bool) preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|webp)$/i', $filename);
    }

    /**
     * @param array{extension:string, name?:string} $imageFile
     */
    private function generateStoredFilename(array $imageFile): ?string
    {
        $extension = strtolower($imageFile['extension'] ?? '');
        if ($extension === '') {
            return null;
        }

        $originalName = (string) ($imageFile['name'] ?? '');
        $base = $this->slugifyFilename($originalName);
        if ($base === '') {
            $base = 'category-image';
        }

        $timestamp = new DateTimeImmutable('now', new DateTimeZone(date_default_timezone_get()));
        $timePart = $timestamp->format('Ymd-His');

        for ($attempt = 0; $attempt < 5; $attempt++) {
            $suffix = $timePart;
            if ($attempt > 0) {
                $suffix .= '-' . $attempt;
            }

            $candidate = sprintf('%s-%s.%s', $base, $suffix, $extension);

            if (!$this->storedFilenameExists($candidate)) {
                return $candidate;
            }
        }

        // Fallback to a random suffix if all attempts collided
        $randomSuffix = $timePart . '-' . bin2hex(random_bytes(2));
        return sprintf('%s-%s.%s', $base, $randomSuffix, $extension);
    }

    private function slugifyFilename(string $originalName): string
    {
        $name = trim(pathinfo($originalName, PATHINFO_FILENAME) ?: '');
        if ($name === '') {
            return '';
        }

        $transliterated = iconv('UTF-8', 'ASCII//TRANSLIT', $name);
        if (!is_string($transliterated)) {
            $transliterated = $name;
        }

        $sanitised = preg_replace('/[^A-Za-z0-9]+/', '-', strtolower($transliterated));
        $sanitised = trim((string) $sanitised, '-');

        return $sanitised;
    }

    private function storedFilenameExists(string $filename): bool
    {
        foreach ($this->uploadLocations as $location) {
            $path = rtrim($location['absolute'], "/\\") . '/' . $filename;
            if (is_file($path)) {
                return true;
            }
        }

        return false;
    }
}
